const express = require('express');
const Permission = require('../models/Permission');
const { authMiddleware } = require('../middleware/auth');
const { hasPermission } = require('../middleware/permission');
const router = express.Router();

// 获取所有权限（分页）
router.get('/', authMiddleware, async (req, res) => {
  try {
    const { page = 1, pageSize = 10 } = req.query;
    const result = await Permission.findAll(parseInt(page), parseInt(pageSize));

    res.json({
      code: 200,
      data: result.list,
      total: result.total,
      page: result.page,
      pageSize: result.pageSize,
      message: '获取权限列表成功'
    });
  } catch (error) {
    console.error('获取权限列表失败:', error);
    res.status(500).json({
      code: 500,
      data: null,
      message: '获取权限列表失败'
    });
  }
});

// 获取权限分组
router.get('/groups', authMiddleware, async (req, res) => {
  try {
    const groups = await Permission.findAllGrouped();

    res.json({
      code: 200,
      data: groups,
      message: '获取权限分组成功'
    });
  } catch (error) {
    console.error('获取权限分组失败:', error);
    res.status(500).json({
      code: 500,
      data: null,
      message: '获取权限分组失败'
    });
  }
});

// 获取权限分组列表
router.get('/group-list', authMiddleware, async (req, res) => {
  try {
    const groups = await Permission.getGroups();

    res.json({
      code: 200,
      data: groups,
      message: '获取权限分组列表成功'
    });
  } catch (error) {
    console.error('获取权限分组列表失败:', error);
    res.status(500).json({
      code: 500,
      data: null,
      message: '获取权限分组列表失败'
    });
  }
});

// 根据ID获取权限详情 - 需要权限查询权限
router.get('/:id', authMiddleware, hasPermission('permission:query'), async (req, res) => {
  try {
    const { id } = req.params;
    const permission = await Permission.findById(id);

    if (!permission) {
      return res.status(404).json({
        code: 404,
        data: null,
        message: '权限不存在'
      });
    }

    res.json({
      code: 200,
      data: permission,
      message: '获取权限详情成功'
    });
  } catch (error) {
    console.error('获取权限详情失败:', error);
    res.status(500).json({
      code: 500,
      data: null,
      message: '获取权限详情失败'
    });
  }
});

// 创建权限
router.post('/', authMiddleware, async (req, res) => {
  try {
    const { code, name, description, group_code, group_name, sort_order, status } = req.body;

    // 简单验证
    if (!code || !name || !group_code || !group_name) {
      return res.status(400).json({
        code: 400,
        data: null,
        message: '权限代码、权限名称、分组代码和分组名称都是必填项'
      });
    }

    // 检查权限代码是否已存在
    const existingPermission = await Permission.findByCode(code);
    if (existingPermission) {
      return res.status(400).json({
        code: 400,
        data: null,
        message: '权限代码已存在'
      });
    }

    const permission = await Permission.create({
      code,
      name,
      description,
      group_code,
      group_name,
      sort_order,
      status
    });

    res.status(200).json({
      code: 200,
      data: permission,
      message: '权限创建成功'
    });
  } catch (error) {
    console.error('创建权限失败:', error);
    res.status(500).json({
      code: 500,
      data: null,
      message: '创建权限失败'
    });
  }
});

// 更新权限
router.put('/:id', authMiddleware, async (req, res) => {
  try {
    const { id } = req.params;
    const { code, name, description, group_code, group_name, sort_order, status } = req.body;

    if (!code || !name || !group_code || !group_name) {
      return res.status(400).json({
        code: 400,
        data: null,
        message: '权限代码、权限名称、分组代码和分组名称都是必填项'
      });
    }

    // 检查权限代码是否已被其他权限使用
    const existingPermission = await Permission.findByCode(code);
    if (existingPermission && existingPermission.id !== id) {
      return res.status(400).json({
        code: 400,
        data: null,
        message: '权限代码已被其他权限使用'
      });
    }

    const permission = await Permission.update(id, {
      code,
      name,
      description,
      group_code,
      group_name,
      sort_order,
      status
    });

    if (!permission) {
      return res.status(404).json({
        code: 404,
        data: null,
        message: '权限不存在'
      });
    }

    res.json({
      code: 200,
      data: permission,
      message: '权限更新成功'
    });
  } catch (error) {
    console.error('更新权限失败:', error);
    res.status(500).json({
      code: 500,
      data: null,
      message: '更新权限失败'
    });
  }
});

// 删除权限 - 需要权限删除权限
router.delete('/:id', authMiddleware, hasPermission('permission:delete'), async (req, res) => {
  try {
    const { id } = req.params;
    const deleted = await Permission.delete(id);

    if (!deleted) {
      return res.status(404).json({
        code: 404,
        data: null,
        message: '权限不存在'
      });
    }

    res.json({
      code: 200,
      data: null,
      message: '权限删除成功'
    });
  } catch (error) {
    console.error('删除权限失败:', error);
    res.status(500).json({
      code: 500,
      data: null,
      message: '删除权限失败'
    });
  }
});

module.exports = router;

module.exports = router;
